/**
 * API客户端模块
 * 提供与后端数据库交互的API接口
 */

class ApiClient {
    constructor() {
        this.baseUrl = '';
        this.currentUser = null;
    }

    /**
     * 发送HTTP请求的通用方法
     */
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin', // 包含session cookie
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers,
            },
        };

        try {
            const response = await fetch(this.baseUrl + url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // ==================== 用户认证相关 ====================

    /**
     * 获取当前用户信息
     */
    async getCurrentUser() {
        try {
            const data = await this.request('/api/auth/user');
            if (data.success) {
                this.currentUser = data.user;
                return data.user;
            }
            return null;
        } catch (error) {
            this.currentUser = null;
            return null;
        }
    }

    /**
     * 用户登录
     */
    async login(username, password) {
        const data = await this.request('/api/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password }),
        });
        
        if (data.success) {
            this.currentUser = data.user;
        }
        
        return data;
    }

    /**
     * 用户登出
     */
    async logout() {
        const data = await this.request('/api/auth/logout', {
            method: 'POST',
        });
        
        this.currentUser = null;
        return data;
    }

    // ==================== 表单记录相关 ====================

    /**
     * 获取用户的表单记录
     */
    async getRecords(recordType = null) {
        const url = recordType ? `/api/records?type=${recordType}` : '/api/records';
        const data = await this.request(url);
        return data.success ? data.records : [];
    }

    /**
     * 获取特定记录
     */
    async getRecord(recordId) {
        const data = await this.request(`/api/records/${recordId}`);
        return data.success ? data.record : null;
    }

    /**
     * 保存表单记录
     */
    async saveRecord(recordId, title, formData, recordType = 'draft') {
        const data = await this.request('/api/records', {
            method: 'POST',
            body: JSON.stringify({
                record_id: recordId,
                title: title,
                form_data: formData,
                record_type: recordType,
            }),
        });
        return data;
    }

    /**
     * 删除记录
     */
    async deleteRecord(recordId) {
        const data = await this.request(`/api/records/${recordId}`, {
            method: 'DELETE',
        });
        return data;
    }

    // ==================== 对话管理相关 ====================

    /**
     * 获取用户的对话列表
     */
    async getConversations() {
        const data = await this.request('/api/conversations');
        return data.success ? data.conversations : [];
    }

    /**
     * 创建新对话
     */
    async createConversation(conversationId, title, description = null) {
        const data = await this.request('/api/conversations', {
            method: 'POST',
            body: JSON.stringify({
                conversation_id: conversationId,
                title: title,
                description: description,
            }),
        });
        return data;
    }

    // ==================== 版本管理相关 ====================

    /**
     * 获取对话的所有版本
     */
    async getVersions(conversationId) {
        const data = await this.request(`/api/conversations/${conversationId}/versions`);
        return data.success ? data.versions : [];
    }

    /**
     * 添加新版本
     */
    async addVersion(conversationId, versionId, versionName, formData, createdBy = 'user', modificationNotes = null) {
        const data = await this.request(`/api/conversations/${conversationId}/versions`, {
            method: 'POST',
            body: JSON.stringify({
                version_id: versionId,
                version_name: versionName,
                form_data: formData,
                created_by: createdBy,
                modification_notes: modificationNotes,
            }),
        });
        return data;
    }

    /**
     * 创建新版本（addVersion的别名，保持API一致性）
     */
    async createVersion(conversationId, versionId, versionName, formData, createdBy = 'user') {
        return this.addVersion(conversationId, versionId, versionName, formData, createdBy);
    }

    /**
     * 获取活跃版本
     */
    async getActiveVersion(conversationId) {
        const data = await this.request(`/api/conversations/${conversationId}/versions/active`);
        return data.success ? data.version : null;
    }

    /**
     * 设置活跃版本
     */
    async setActiveVersion(conversationId, versionId) {
        const data = await this.request(`/api/conversations/${conversationId}/versions/${versionId}/activate`, {
            method: 'POST',
        });
        return data;
    }

    /**
     * 更新版本数据
     */
    async updateVersion(conversationId, versionId, formData, versionName = null, modificationNotes = null) {
        const updateData = {
            form_data: formData
        };

        if (versionName !== null) {
            updateData.version_name = versionName;
        }

        if (modificationNotes !== null) {
            updateData.modification_notes = modificationNotes;
        }

        const data = await this.request(`/api/conversations/${conversationId}/versions/${versionId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData),
        });
        return data;
    }

    // ==================== 聊天消息相关 ====================

    /**
     * 获取对话的聊天消息
     */
    async getMessages(conversationId) {
        const data = await this.request(`/api/conversations/${conversationId}/messages`);
        return data.success ? data.messages : [];
    }

    /**
     * 添加聊天消息
     */
    async addMessage(conversationId, messageId, sender, message, versionBefore = null, versionAfter = null) {
        const data = await this.request(`/api/conversations/${conversationId}/messages`, {
            method: 'POST',
            body: JSON.stringify({
                message_id: messageId,
                sender: sender,
                message: message,
                version_before: versionBefore,
                version_after: versionAfter,
            }),
        });
        return data;
    }

    // ==================== 工具方法 ====================

    /**
     * 检查用户是否已登录
     */
    async isLoggedIn() {
        const user = await this.getCurrentUser();
        return user !== null;
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 检查表单是否为空
     */
    isFormEmpty(formData) {
        if (!formData || typeof formData !== 'object') return true;
        
        for (const key in formData) {
            if (formData[key] && String(formData[key]).trim() !== '') {
                return false;
            }
        }
        return true;
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('API错误:', message);
        // 可以在这里添加用户界面的错误提示
        if (window.showToast) {
            window.showToast(message, 'error');
        } else {
            alert('错误: ' + message);
        }
    }

    /**
     * 处理认证错误
     */
    handleAuthError() {
        this.currentUser = null;
        // 重定向到登录页面
        if (window.location.pathname !== '/login' && window.location.pathname !== '/register') {
            window.location.href = '/login';
        }
    }
}

// 创建全局API客户端实例
window.apiClient = new ApiClient();

// 页面加载时检查用户登录状态
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const user = await window.apiClient.getCurrentUser();
        if (!user && window.location.pathname !== '/login' && window.location.pathname !== '/register') {
            // 用户未登录且不在登录/注册页面，重定向到登录页面
            window.location.href = '/login';
        }
    } catch (error) {
        console.error('检查登录状态失败:', error);
    }
});

// 导出API客户端类
window.ApiClient = ApiClient;

/**
 * 浏览器控制台调试脚本
 * 在浏览器控制台中运行此脚本来测试修复效果
 */

// 测试管理器初始化状态
function testManagersStatus() {
    console.log('=== 管理器状态检查 ===');
    
    const managers = {
        apiClient: window.apiClient,
        dataManager: window.dataManager,
        conversationManager: window.conversationManager,
        conversationUI: window.conversationUI,
        autoSaveManager: window.autoSaveManager
    };
    
    for (const [name, manager] of Object.entries(managers)) {
        if (manager) {
            console.log(`✅ ${name}: 已初始化`);
        } else {
            console.log(`❌ ${name}: 未初始化`);
        }
    }
    
    return managers;
}

// 测试API连接
async function testApiConnection() {
    console.log('=== API连接测试 ===');
    
    if (!window.apiClient) {
        console.log('❌ API客户端未初始化');
        return false;
    }
    
    try {
        // 测试获取当前用户
        const user = await window.apiClient.getCurrentUser();
        if (user) {
            console.log('✅ 用户已登录:', user.username);
            return true;
        } else {
            console.log('⚠️ 用户未登录');
            return false;
        }
    } catch (error) {
        console.log('❌ API连接失败:', error.message);
        return false;
    }
}

// 测试对话管理器
async function testConversationManager() {
    console.log('=== 对话管理器测试 ===');
    
    if (!window.conversationManager) {
        console.log('❌ 对话管理器未初始化');
        return false;
    }
    
    try {
        // 检查当前对话
        const currentConversation = window.conversationManager.currentConversation;
        if (currentConversation) {
            console.log('✅ 当前对话:', currentConversation.title);
            console.log('对话ID:', currentConversation.id);
            
            // 检查活跃版本
            const activeVersion = currentConversation.form_versions?.find(v => v.is_active);
            if (activeVersion) {
                console.log('✅ 活跃版本:', activeVersion.version_name);
                console.log('版本ID:', activeVersion.version_id);
                
                // 检查表单数据
                if (activeVersion.form_data) {
                    console.log('✅ 表单数据存在，字段数量:', Object.keys(activeVersion.form_data).length);
                    console.log('表单数据示例:', Object.keys(activeVersion.form_data).slice(0, 5));
                } else {
                    console.log('⚠️ 表单数据为空');
                }
            } else {
                console.log('⚠️ 没有活跃版本');
            }
        } else {
            console.log('⚠️ 没有当前对话');
        }
        
        // 检查所有对话
        const conversations = window.conversationManager.getAllConversations();
        console.log(`对话总数: ${conversations.length}`);
        
        return true;
    } catch (error) {
        console.log('❌ 对话管理器测试失败:', error.message);
        return false;
    }
}

// 测试表单数据收集
function testFormDataCollection() {
    console.log('=== 表单数据收集测试 ===');
    
    try {
        // 检查是否有collectFormData函数
        if (typeof collectFormData === 'function') {
            const formData = collectFormData();
            console.log('✅ 表单数据收集成功');
            console.log('收集到的字段数量:', Object.keys(formData).length);
            console.log('字段示例:', Object.keys(formData).slice(0, 10));
            
            // 检查是否有数据
            const hasData = Object.values(formData).some(value => value && value.trim && value.trim() !== '');
            if (hasData) {
                console.log('✅ 表单中有数据');
            } else {
                console.log('⚠️ 表单中没有数据');
            }
            
            return formData;
        } else {
            console.log('❌ collectFormData函数不存在');
            return null;
        }
    } catch (error) {
        console.log('❌ 表单数据收集失败:', error.message);
        return null;
    }
}

// 测试自动保存
function testAutoSave() {
    console.log('=== 自动保存测试 ===');
    
    if (!window.autoSaveManager) {
        console.log('❌ 自动保存管理器未初始化');
        return false;
    }
    
    try {
        console.log('自动保存状态:', window.autoSaveManager.isEnabled ? '已启用' : '已禁用');
        console.log('当前对话ID:', window.autoSaveManager.currentConversationId);
        console.log('当前版本ID:', window.autoSaveManager.currentVersionId);
        
        return true;
    } catch (error) {
        console.log('❌ 自动保存测试失败:', error.message);
        return false;
    }
}

// 模拟页面刷新后的数据恢复
async function simulateDataRecovery() {
    console.log('=== 模拟数据恢复测试 ===');
    
    try {
        // 1. 检查管理器状态
        const managersOk = testManagersStatus();
        if (!managersOk.conversationManager || !managersOk.apiClient) {
            console.log('❌ 必要的管理器未初始化，无法进行数据恢复');
            return false;
        }
        
        // 2. 测试API连接
        const apiOk = await testApiConnection();
        if (!apiOk) {
            console.log('⚠️ API连接失败，可能用户未登录');
        }
        
        // 3. 尝试加载对话数据
        if (apiOk) {
            console.log('尝试从API加载对话数据...');
            await window.conversationManager.loadConversationsFromAPI();
            await window.conversationManager.restoreActiveConversation();
        }
        
        // 4. 检查恢复结果
        await testConversationManager();
        
        // 5. 测试表单数据
        testFormDataCollection();
        
        // 6. 测试自动保存
        testAutoSave();
        
        console.log('=== 数据恢复测试完成 ===');
        return true;
    } catch (error) {
        console.log('❌ 数据恢复测试失败:', error.message);
        return false;
    }
}

// 运行完整测试
async function runFullTest() {
    console.log('🚀 开始运行完整测试...');
    console.log('');
    
    // 等待一下确保页面完全加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await simulateDataRecovery();
    
    console.log('');
    console.log('✨ 测试完成！');
    console.log('');
    console.log('如果看到错误，请检查：');
    console.log('1. 用户是否已登录');
    console.log('2. 管理器是否正确初始化');
    console.log('3. API服务器是否正常运行');
}

// 导出测试函数到全局作用域
window.testManagersStatus = testManagersStatus;
window.testApiConnection = testApiConnection;
window.testConversationManager = testConversationManager;
window.testFormDataCollection = testFormDataCollection;
window.testAutoSave = testAutoSave;
window.simulateDataRecovery = simulateDataRecovery;
window.runFullTest = runFullTest;

console.log('🔧 调试脚本已加载！');
console.log('可用的测试函数：');
console.log('- testManagersStatus() - 检查管理器状态');
console.log('- testApiConnection() - 测试API连接');
console.log('- testConversationManager() - 测试对话管理器');
console.log('- testFormDataCollection() - 测试表单数据收集');
console.log('- testAutoSave() - 测试自动保存');
console.log('- simulateDataRecovery() - 模拟数据恢复');
console.log('- runFullTest() - 运行完整测试');
console.log('');
console.log('建议先运行: runFullTest()');

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>认证状态测试</h1>
    
    <div id="status" class="status warning">检查中...</div>
    
    <div>
        <button onclick="checkAuthStatus()">检查认证状态</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        let logEntries = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            logEntries.push(entry);
            document.getElementById('log').textContent = logEntries.join('\n');
            console.log(entry);
        }
        
        function clearLog() {
            logEntries = [];
            document.getElementById('log').textContent = '';
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        async function checkAuthStatus() {
            log('开始检查认证状态...');
            
            try {
                // 检查当前用户
                const response = await fetch('/api/auth/user');
                const result = await response.json();
                
                log(`认证检查响应: ${JSON.stringify(result)}`);
                
                if (result.success && result.user) {
                    log(`用户已登录: ${result.user.username}`);
                    updateStatus(`已登录: ${result.user.username}`, 'success');
                } else {
                    log('用户未登录');
                    updateStatus('用户未登录', 'error');
                }
            } catch (error) {
                log(`认证检查失败: ${error.message}`);
                updateStatus(`认证检查失败: ${error.message}`, 'error');
            }
        }
        
        async function testLogin() {
            log('开始测试登录...');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test_user',
                        password: 'test_password'
                    })
                });
                
                const result = await response.json();
                log(`登录响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    log('登录成功');
                    updateStatus('登录成功', 'success');
                    
                    // 重新检查认证状态
                    setTimeout(checkAuthStatus, 1000);
                } else {
                    log(`登录失败: ${result.message}`);
                    updateStatus(`登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`登录错误: ${error.message}`);
                updateStatus(`登录错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面已加载，开始检查认证状态');
            checkAuthStatus();
        });
    </script>
</body>
</html>

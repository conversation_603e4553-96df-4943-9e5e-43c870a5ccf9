<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话恢复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>8D系统会话恢复测试</h1>
    
    <div class="test-section">
        <h3>测试步骤</h3>
        <ol>
            <li>点击"模拟登录"按钮</li>
            <li>点击"创建测试对话"按钮</li>
            <li>点击"模拟页面刷新"按钮</li>
            <li>观察数据是否正确恢复</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>测试控制</h3>
        <button onclick="simulateLogin()">模拟登录</button>
        <button onclick="createTestConversation()">创建测试对话</button>
        <button onclick="simulatePageRefresh()">模拟页面刷新</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h3>测试结果</h3>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h3>详细日志</h3>
        <div id="testLog" class="log"></div>
    </div>

    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('testLog');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('testLog').textContent = '';
            document.getElementById('testResults').innerHTML = '';
        }
        
        function updateResults(message, type) {
            const resultsElement = document.getElementById('testResults');
            resultsElement.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        async function simulateLogin() {
            log('开始模拟登录...');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test_user',
                        password: 'test_password'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('登录成功');
                    updateResults('登录成功', 'success');
                } else {
                    log(`登录失败: ${result.message}`);
                    updateResults(`登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`登录错误: ${error.message}`);
                updateResults(`登录错误: ${error.message}`, 'error');
            }
        }
        
        async function createTestConversation() {
            log('开始创建测试对话...');
            
            try {
                const conversationId = 'test_conv_' + Date.now();
                const response = await fetch('/api/conversations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        conversation_id: conversationId,
                        title: '测试对话',
                        description: '用于测试会话恢复的对话'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`对话创建成功: ${conversationId}`);
                    
                    // 创建测试版本
                    const versionResponse = await fetch(`/api/conversations/${conversationId}/versions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            version_id: 'test_version_1',
                            version_name: '测试版本',
                            form_data: {
                                problem_description: '这是一个测试问题描述',
                                team_leader: '测试负责人',
                                member1: '测试成员1',
                                member2: '测试成员2'
                            },
                            created_by: 'test_user'
                        })
                    });
                    
                    const versionResult = await versionResponse.json();
                    
                    if (versionResult.success) {
                        log('测试版本创建成功');
                        updateResults('测试对话和版本创建成功', 'success');
                        
                        // 保存到sessionStorage以便测试恢复
                        sessionStorage.setItem('test_conversation_id', conversationId);
                        sessionStorage.setItem('test_version_id', 'test_version_1');
                    } else {
                        log(`版本创建失败: ${versionResult.message}`);
                        updateResults(`版本创建失败: ${versionResult.message}`, 'error');
                    }
                } else {
                    log(`对话创建失败: ${result.message}`);
                    updateResults(`对话创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`创建对话错误: ${error.message}`);
                updateResults(`创建对话错误: ${error.message}`, 'error');
            }
        }
        
        async function simulatePageRefresh() {
            log('开始模拟页面刷新和数据恢复...');
            
            try {
                const conversationId = sessionStorage.getItem('test_conversation_id');
                const versionId = sessionStorage.getItem('test_version_id');
                
                if (!conversationId || !versionId) {
                    log('没有找到测试对话信息，请先创建测试对话');
                    updateResults('没有找到测试对话信息，请先创建测试对话', 'warning');
                    return;
                }
                
                log(`尝试恢复对话: ${conversationId}, 版本: ${versionId}`);
                
                // 1. 获取对话列表
                const conversationsResponse = await fetch('/api/conversations');
                const conversationsResult = await conversationsResponse.json();
                
                if (conversationsResult.success) {
                    log(`成功获取 ${conversationsResult.conversations.length} 个对话`);
                    
                    // 2. 查找测试对话
                    const testConversation = conversationsResult.conversations.find(
                        conv => conv.conversation_id === conversationId
                    );
                    
                    if (testConversation) {
                        log('找到测试对话');
                        
                        // 3. 获取对话详情
                        const detailResponse = await fetch(`/api/conversations/${conversationId}`);
                        const detailResult = await detailResponse.json();
                        
                        if (detailResult.success) {
                            log('成功获取对话详情');
                            
                            // 4. 检查版本数据
                            const conversation = detailResult.conversation;
                            const activeVersion = conversation.form_versions?.find(v => v.is_active);
                            
                            if (activeVersion) {
                                log(`找到活跃版本: ${activeVersion.version_id}`);
                                log(`版本数据: ${JSON.stringify(activeVersion.form_data, null, 2)}`);
                                
                                updateResults('数据恢复成功！找到活跃版本和表单数据', 'success');
                            } else {
                                log('未找到活跃版本');
                                updateResults('数据恢复失败：未找到活跃版本', 'error');
                            }
                        } else {
                            log(`获取对话详情失败: ${detailResult.message}`);
                            updateResults(`获取对话详情失败: ${detailResult.message}`, 'error');
                        }
                    } else {
                        log('未找到测试对话');
                        updateResults('数据恢复失败：未找到测试对话', 'error');
                    }
                } else {
                    log(`获取对话列表失败: ${conversationsResult.message}`);
                    updateResults(`获取对话列表失败: ${conversationsResult.message}`, 'error');
                }
            } catch (error) {
                log(`数据恢复错误: ${error.message}`);
                updateResults(`数据恢复错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面已加载');
            updateResults('请按照测试步骤进行操作', 'warning');
        });
    </script>
</body>
</html>

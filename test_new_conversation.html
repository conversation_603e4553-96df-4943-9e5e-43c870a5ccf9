<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增对话测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>新增对话测试</h1>
    
    <div id="status" class="status warning">准备测试...</div>
    
    <div class="form-group">
        <label for="conversationTitle">对话标题:</label>
        <input type="text" id="conversationTitle" value="测试对话 - " placeholder="输入对话标题">
    </div>
    
    <div class="form-group">
        <label for="conversationDescription">对话描述:</label>
        <textarea id="conversationDescription" rows="3" placeholder="输入对话描述（可选）">这是一个测试对话，用于验证新增对话功能。</textarea>
    </div>
    
    <div>
        <button onclick="testLogin()">1. 测试登录</button>
        <button onclick="testCreateConversation()">2. 创建对话</button>
        <button onclick="testAddFormData()">3. 添加表单数据</button>
        <button onclick="testFullWorkflow()">4. 完整流程测试</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        let logEntries = [];
        let testConversationId = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            logEntries.push(entry);
            document.getElementById('log').textContent = logEntries.join('\n');
            console.log(entry);
        }
        
        function clearLog() {
            logEntries = [];
            document.getElementById('log').textContent = '';
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        // 生成唯一ID
        function generateId() {
            return 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        async function testLogin() {
            log('开始测试登录...');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test_user',
                        password: 'test_password'
                    })
                });
                
                const result = await response.json();
                log(`登录响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    log('✅ 登录成功');
                    updateStatus('已登录', 'success');
                    
                    // 检查当前用户
                    const userResponse = await fetch('/api/auth/user');
                    const userResult = await userResponse.json();
                    if (userResult.success) {
                        log(`当前用户: ${userResult.user.username}`);
                    }
                    
                    return true;
                } else {
                    log(`❌ 登录失败: ${result.message}`);
                    updateStatus(`登录失败: ${result.message}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 登录错误: ${error.message}`);
                updateStatus(`登录错误: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testCreateConversation() {
            log('开始测试创建对话...');
            
            try {
                // 生成对话数据
                testConversationId = generateId();
                const title = document.getElementById('conversationTitle').value + new Date().toLocaleTimeString();
                const description = document.getElementById('conversationDescription').value;
                
                log(`创建对话: ID=${testConversationId}, 标题=${title}`);
                
                const response = await fetch('/api/conversations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        conversation_id: testConversationId,
                        title: title,
                        description: description
                    })
                });
                
                const result = await response.json();
                log(`创建对话响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    log('✅ 对话创建成功');
                    log(`返回的对话ID: ${result.conversation_id}`);
                    updateStatus('对话创建成功', 'success');
                    
                    // 验证对话是否可以获取
                    await testGetConversation(testConversationId);
                    
                    return true;
                } else {
                    log(`❌ 对话创建失败: ${result.message}`);
                    updateStatus(`对话创建失败: ${result.message}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 创建对话错误: ${error.message}`);
                updateStatus(`创建对话错误: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testGetConversation(conversationId) {
            log(`测试获取对话: ${conversationId}`);
            
            try {
                const response = await fetch(`/api/conversations/${conversationId}`);
                const result = await response.json();
                
                if (result.success) {
                    log('✅ 对话获取成功');
                    log(`对话详情: ${JSON.stringify(result.conversation, null, 2)}`);
                    return result.conversation;
                } else {
                    log(`❌ 对话获取失败: ${result.message}`);
                    return null;
                }
            } catch (error) {
                log(`❌ 获取对话错误: ${error.message}`);
                return null;
            }
        }
        
        async function testAddFormData() {
            if (!testConversationId) {
                log('❌ 请先创建对话');
                return false;
            }
            
            log('开始测试添加表单版本...');
            
            try {
                const versionId = 'ver_' + Date.now();
                const formData = {
                    problem_description: '测试问题描述',
                    team_members: '张三, 李四, 王五',
                    problem_date: '2024-01-15',
                    containment_actions: '立即停止生产，隔离问题产品'
                };
                
                log(`添加版本: ID=${versionId}`);
                log(`表单数据: ${JSON.stringify(formData, null, 2)}`);
                
                const response = await fetch(`/api/conversations/${testConversationId}/versions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        version_id: versionId,
                        version_name: '初始版本',
                        form_data: formData,
                        created_by: 'user'
                    })
                });
                
                const result = await response.json();
                log(`添加版本响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    log('✅ 版本添加成功');
                    updateStatus('版本添加成功', 'success');
                    
                    // 获取版本列表验证
                    await testGetVersions(testConversationId);
                    
                    return true;
                } else {
                    log(`❌ 版本添加失败: ${result.message}`);
                    updateStatus(`版本添加失败: ${result.message}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 添加版本错误: ${error.message}`);
                updateStatus(`添加版本错误: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testGetVersions(conversationId) {
            log(`测试获取版本列表: ${conversationId}`);
            
            try {
                const response = await fetch(`/api/conversations/${conversationId}/versions`);
                const result = await response.json();
                
                if (result.success) {
                    log('✅ 版本列表获取成功');
                    log(`版本数量: ${result.versions.length}`);
                    result.versions.forEach((version, index) => {
                        log(`版本 ${index + 1}: ${version.version_name} (${version.version_id})`);
                    });
                    return result.versions;
                } else {
                    log(`❌ 版本列表获取失败: ${result.message}`);
                    return [];
                }
            } catch (error) {
                log(`❌ 获取版本列表错误: ${error.message}`);
                return [];
            }
        }
        
        async function testFullWorkflow() {
            log('🚀 开始完整流程测试...');
            log('');
            
            updateStatus('正在执行完整流程测试...', 'warning');
            
            // 1. 登录
            const loginSuccess = await testLogin();
            if (!loginSuccess) {
                log('❌ 登录失败，停止测试');
                return;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 2. 创建对话
            const createSuccess = await testCreateConversation();
            if (!createSuccess) {
                log('❌ 创建对话失败，停止测试');
                return;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 3. 添加表单数据
            const addDataSuccess = await testAddFormData();
            if (!addDataSuccess) {
                log('❌ 添加表单数据失败，停止测试');
                return;
            }
            
            log('');
            log('✨ 完整流程测试成功！');
            updateStatus('完整流程测试成功', 'success');
        }
        
        // 页面加载时自动更新标题
        document.addEventListener('DOMContentLoaded', function() {
            const titleInput = document.getElementById('conversationTitle');
            titleInput.value = titleInput.value + new Date().toLocaleString();
            log('页面已加载，准备开始测试');
        });
    </script>
</body>
</html>

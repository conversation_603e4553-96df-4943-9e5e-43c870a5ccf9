/**
 * 自动保存管理器
 * 负责监听表单变化并自动保存到后端
 */

class AutoSaveManager {
    constructor() {
        this.saveTimeout = null;
        this.saveDelay = 2000; // 2秒延迟保存
        this.isEnabled = true;
        this.lastSaveData = null;
        this.saveIndicator = null;
        this.currentConversationId = null;
        this.currentVersionId = null;
        this.contextRestored = false;
        this.retryAttempts = 0;
        this.maxRetryAttempts = 5;

        this.initSaveIndicator();
        this.initFormListeners();
        this.initContextRecovery();
        this.initStatusMonitoring();
    }

    /**
     * 初始化保存状态指示器
     */
    initSaveIndicator() {
        // 创建保存状态指示器
        this.saveIndicator = document.createElement('div');
        this.saveIndicator.id = 'auto-save-indicator';
        this.saveIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            transition: all 0.3s ease;
            display: none;
        `;
        // 确保DOM已准备好再添加元素
        if (document.body) {
            document.body.appendChild(this.saveIndicator);
        } else {
            // 如果body还没准备好，等待DOM加载完成
            document.addEventListener('DOMContentLoaded', () => {
                if (document.body) {
                    document.body.appendChild(this.saveIndicator);
                }
            });
        }
    }

    /**
     * 显示保存状态
     */
    showSaveStatus(status, message) {
        if (!this.saveIndicator) return;

        const styles = {
            saving: {
                backgroundColor: '#fef3c7',
                color: '#92400e',
                border: '1px solid #fbbf24'
            },
            success: {
                backgroundColor: '#d1fae5',
                color: '#065f46',
                border: '1px solid #10b981'
            },
            error: {
                backgroundColor: '#fee2e2',
                color: '#991b1b',
                border: '1px solid #ef4444'
            }
        };

        const style = styles[status] || styles.saving;
        Object.assign(this.saveIndicator.style, style);
        this.saveIndicator.textContent = message;
        this.saveIndicator.style.display = 'block';

        // 自动隐藏成功和错误状态
        if (status === 'success' || status === 'error') {
            setTimeout(() => {
                if (this.saveIndicator) {
                    this.saveIndicator.style.display = 'none';
                }
            }, 3000);
        }
    }

    /**
     * 初始化表单监听器
     */
    initFormListeners() {
        // 监听所有表单输入
        document.addEventListener('input', (e) => {
            if (this.shouldAutoSave(e.target)) {
                this.scheduleAutoSave();
            }
        });

        // 监听选择框变化
        document.addEventListener('change', (e) => {
            if (this.shouldAutoSave(e.target)) {
                this.scheduleAutoSave();
            }
        });

        // 监听动态元素的添加和删除
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-member-btn, .remove-member-btn, .add-measure-btn, .remove-measure-btn')) {
                // 延迟保存，等待DOM更新
                setTimeout(() => {
                    this.scheduleAutoSave();
                }, 100);
            }
        });
    }

    /**
     * 判断是否应该自动保存
     */
    shouldAutoSave(element) {
        // 排除某些不需要保存的元素
        const excludeSelectors = [
            '#auto-save-indicator',
            '.search-input',
            '.filter-input',
            '[data-no-autosave]'
        ];

        for (const selector of excludeSelectors) {
            if (element.matches && element.matches(selector)) {
                return false;
            }
        }

        // 如果上下文未恢复，尝试恢复
        if (!this.contextRestored || !this.currentConversationId || !this.currentVersionId) {
            this.checkAndRecoverContext();
            return false; // 本次不保存，等待上下文恢复
        }

        // 只保存表单相关的元素
        return element.matches('input, textarea, select') &&
               element.form &&
               this.isEnabled &&
               this.currentConversationId &&
               this.currentVersionId;
    }

    /**
     * 安排自动保存
     */
    scheduleAutoSave() {
        if (!this.isEnabled) return;

        // 清除之前的保存计划
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }

        // 安排新的保存
        this.saveTimeout = setTimeout(() => {
            this.performAutoSave();
        }, this.saveDelay);
    }

    /**
     * 执行自动保存
     */
    async performAutoSave() {
        if (!this.isEnabled) return;

        // 检查上下文是否有效
        if (!this.currentConversationId || !this.currentVersionId) {
            console.log('自动保存跳过：上下文无效，尝试恢复...');
            this.checkAndRecoverContext();
            return;
        }

        try {
            // 获取当前表单数据
            const formData = this.collectFormData();

            // 检查数据是否有变化
            const dataString = JSON.stringify(formData);
            if (dataString === this.lastSaveData) {
                console.log('自动保存跳过：数据无变化');
                return;
            }

            this.showSaveStatus('saving', '正在保存...');

            // 调用API保存数据
            const result = await window.apiClient.updateVersion(
                this.currentConversationId,
                this.currentVersionId,
                formData
            );

            if (result.success) {
                this.lastSaveData = dataString;
                this.showSaveStatus('success', '保存成功');
                console.log('自动保存成功');

                // 触发保存成功事件
                this.notifyAutoSaveSuccess(formData);
            } else {
                this.showSaveStatus('error', '保存失败');
                console.error('自动保存失败:', result.message);

                // 如果是上下文相关的错误，尝试恢复上下文
                if (result.message && (result.message.includes('对话') || result.message.includes('版本'))) {
                    console.log('检测到上下文错误，尝试恢复...');
                    this.contextRestored = false;
                    this.checkAndRecoverContext();
                }
            }

        } catch (error) {
            this.showSaveStatus('error', '保存出错');
            console.error('自动保存出错:', error);

            // 如果是网络错误或认证错误，不重置上下文
            if (error.message && !error.message.includes('网络') && !error.message.includes('登录')) {
                this.contextRestored = false;
                this.checkAndRecoverContext();
            }
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = {};
        
        // 获取所有表单元素
        const form = document.querySelector('#d8-form');
        if (!form) return formData;

        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            if (input.name) {
                if (input.type === 'checkbox') {
                    formData[input.name] = input.checked;
                } else if (input.type === 'radio') {
                    if (input.checked) {
                        formData[input.name] = input.value;
                    }
                } else {
                    formData[input.name] = input.value;
                }
            }
        });

        return formData;
    }

    /**
     * 设置当前对话和版本
     */
    setCurrentContext(conversationId, versionId) {
        this.currentConversationId = conversationId;
        this.currentVersionId = versionId;
        this.lastSaveData = null; // 重置保存数据
        this.contextRestored = true;
        this.retryAttempts = 0;

        // 保存上下文到sessionStorage以便页面刷新后恢复
        try {
            sessionStorage.setItem('autoSave_conversationId', conversationId);
            sessionStorage.setItem('autoSave_versionId', versionId);
            sessionStorage.setItem('autoSave_timestamp', Date.now().toString());
        } catch (e) {
            console.warn('无法保存自动保存上下文到sessionStorage:', e);
        }

        console.log(`自动保存上下文设置: 对话=${conversationId}, 版本=${versionId}`);
    }

    /**
     * 启用自动保存
     */
    enable() {
        this.isEnabled = true;
        console.log('自动保存已启用');

        // 启用时检查上下文
        this.checkAndRecoverContext();
    }

    /**
     * 禁用自动保存
     */
    disable() {
        this.isEnabled = false;
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = null;
        }
        console.log('自动保存已禁用');
    }

    /**
     * 获取自动保存状态
     */
    getStatus() {
        return {
            isEnabled: this.isEnabled,
            contextRestored: this.contextRestored,
            currentConversationId: this.currentConversationId,
            currentVersionId: this.currentVersionId,
            retryAttempts: this.retryAttempts,
            hasActiveTimeout: !!this.saveTimeout
        };
    }

    /**
     * 显示自动保存状态诊断信息
     */
    diagnose() {
        const status = this.getStatus();
        console.group('自动保存状态诊断');
        console.log('启用状态:', status.isEnabled);
        console.log('上下文恢复:', status.contextRestored);
        console.log('对话ID:', status.currentConversationId);
        console.log('版本ID:', status.currentVersionId);
        console.log('重试次数:', status.retryAttempts);
        console.log('活跃保存任务:', status.hasActiveTimeout);

        // 检查依赖组件
        console.log('API客户端:', !!window.apiClient);
        console.log('对话管理器:', !!window.conversationManager);
        console.log('对话UI:', !!window.conversationUI);

        // 检查表单状态
        const form = document.querySelector('#d8-form');
        console.log('表单存在:', !!form);
        if (form) {
            const inputs = form.querySelectorAll('input, textarea, select');
            console.log('表单元素数量:', inputs.length);
        }

        console.groupEnd();

        return status;
    }

    /**
     * 立即保存
     */
    async saveNow() {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = null;
        }
        await this.performAutoSave();
    }

    /**
     * 通知自动保存成功
     */
    notifyAutoSaveSuccess(formData) {
        // 触发自定义事件
        const event = new CustomEvent('autoSaveSuccess', {
            detail: { formData, timestamp: new Date() }
        });
        document.dispatchEvent(event);
    }

    /**
     * 初始化上下文恢复机制
     */
    initContextRecovery() {
        // 延迟尝试恢复上下文，等待其他组件初始化完成
        setTimeout(() => {
            this.attemptContextRecovery();
        }, 1000);

        // 监听对话管理器的事件
        document.addEventListener('conversationLoaded', (event) => {
            if (event.detail && !this.contextRestored) {
                this.setCurrentContext(event.detail.conversationId, event.detail.versionId);
            }
        });

        // 监听页面可见性变化，当页面重新可见时检查上下文
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.contextRestored) {
                setTimeout(() => this.attemptContextRecovery(), 500);
            }
        });
    }

    /**
     * 尝试恢复自动保存上下文
     */
    async attemptContextRecovery() {
        if (this.contextRestored || this.retryAttempts >= this.maxRetryAttempts) {
            return;
        }

        this.retryAttempts++;
        console.log(`尝试恢复自动保存上下文 (第${this.retryAttempts}次)`);

        try {
            // 首先尝试从sessionStorage恢复
            const savedConversationId = sessionStorage.getItem('autoSave_conversationId');
            const savedVersionId = sessionStorage.getItem('autoSave_versionId');
            const savedTimestamp = sessionStorage.getItem('autoSave_timestamp');

            // 检查保存的数据是否有效（不超过1小时）
            if (savedConversationId && savedVersionId && savedTimestamp) {
                const age = Date.now() - parseInt(savedTimestamp);
                if (age < 3600000) { // 1小时内有效
                    console.log('从sessionStorage恢复自动保存上下文');
                    this.setCurrentContext(savedConversationId, savedVersionId);
                    return;
                }
            }

            // 如果sessionStorage无效，尝试从对话管理器获取当前活跃对话
            if (window.conversationManager && window.conversationManager.currentConversation) {
                const conversation = window.conversationManager.currentConversation;
                const activeVersion = conversation.form_versions?.find(v => v.is_active);

                if (activeVersion) {
                    console.log('从对话管理器恢复自动保存上下文');
                    this.setCurrentContext(conversation.id, activeVersion.version_id);
                    return;
                }
            }

            // 如果对话管理器还没有当前对话，尝试获取最新的对话
            if (window.conversationManager && window.apiClient) {
                const conversations = await window.apiClient.getConversations();
                if (conversations && conversations.length > 0) {
                    const latestConversation = conversations[0];

                    // 获取该对话的活跃版本
                    const versions = await window.apiClient.getVersions(latestConversation.conversation_id);
                    const activeVersion = versions?.find(v => v.is_active);

                    if (activeVersion) {
                        console.log('从最新对话恢复自动保存上下文');
                        this.setCurrentContext(latestConversation.conversation_id, activeVersion.version_id);
                        return;
                    }
                }
            }

            // 如果所有方法都失败，稍后重试
            if (this.retryAttempts < this.maxRetryAttempts) {
                setTimeout(() => this.attemptContextRecovery(), 2000);
            } else {
                console.warn('无法恢复自动保存上下文，已达到最大重试次数');
                this.showSaveStatus('warning', '自动保存功能需要手动激活');
            }

        } catch (error) {
            console.error('恢复自动保存上下文时出错:', error);
            if (this.retryAttempts < this.maxRetryAttempts) {
                setTimeout(() => this.attemptContextRecovery(), 2000);
            }
        }
    }

    /**
     * 检查并恢复上下文（公共方法）
     */
    checkAndRecoverContext() {
        if (!this.contextRestored) {
            this.attemptContextRecovery();
        }
    }

    /**
     * 初始化状态监控
     */
    initStatusMonitoring() {
        // 定期检查自动保存状态
        setInterval(() => {
            this.performStatusCheck();
        }, 30000); // 每30秒检查一次

        // 监听表单变化，如果检测到用户输入但上下文无效，尝试恢复
        let lastFormCheckTime = 0;
        document.addEventListener('input', (event) => {
            const now = Date.now();
            if (now - lastFormCheckTime > 5000) { // 限制检查频率
                lastFormCheckTime = now;

                if (this.shouldAutoSave(event.target) && !this.contextRestored) {
                    console.log('检测到用户输入但上下文无效，尝试恢复...');
                    this.checkAndRecoverContext();
                }
            }
        });
    }

    /**
     * 执行状态检查
     */
    performStatusCheck() {
        if (!this.isEnabled) return;

        // 检查上下文是否有效
        if (!this.contextRestored || !this.currentConversationId || !this.currentVersionId) {
            console.log('状态检查：上下文无效，尝试恢复...');
            this.checkAndRecoverContext();
            return;
        }

        // 检查依赖组件是否正常
        if (!window.apiClient || !window.conversationManager) {
            console.warn('状态检查：依赖组件缺失');
            return;
        }

        // 检查表单是否存在
        const form = document.querySelector('#d8-form');
        if (!form) {
            console.warn('状态检查：表单不存在');
            return;
        }

        // 如果一切正常，更新状态指示器
        if (this.contextRestored && this.currentConversationId && this.currentVersionId) {
            // 可以在这里显示一个轻微的状态指示
            console.log('状态检查：自动保存功能正常');
        }
    }

    /**
     * 销毁自动保存管理器
     */
    destroy() {
        this.disable();
        if (this.saveIndicator) {
            this.saveIndicator.remove();
            this.saveIndicator = null;
        }

        // 清理sessionStorage
        try {
            sessionStorage.removeItem('autoSave_conversationId');
            sessionStorage.removeItem('autoSave_versionId');
            sessionStorage.removeItem('autoSave_timestamp');
        } catch (e) {
            // 忽略清理错误
        }
    }
}

// 创建全局实例
window.autoSaveManager = new AutoSaveManager();

// 监听自动保存成功事件
document.addEventListener('autoSaveSuccess', (e) => {
    console.log('自动保存成功事件:', e.detail);
});

console.log('自动保存管理器已初始化');
